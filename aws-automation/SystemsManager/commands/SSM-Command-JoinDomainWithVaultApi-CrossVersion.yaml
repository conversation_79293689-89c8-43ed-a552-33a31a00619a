---
schemaVersion: "2.2"
description: "Join EC2 instance to Active Directory domain using credentials from <PERSON><PERSON><PERSON><PERSON><PERSON> (PowerShell 5.1 and 7.4+ compatible)"
parameters:
  hostname:
    type: String
    description: "The computer name to use when joining the domain (must match pre-created AD object)"
    allowedPattern: "^[a-zA-Z0-9-]{1,15}$"
  domainName:
    type: String
    description: "The fully qualified domain name to join (e.g., mud.internal.co.za)"
  localAdminGroups:
    type: String
    description: "Comma-separated list of domain groups to add to local Administrators group (e.g., MUD\\DL-Group1,MUD\\DL-Group2)"
    default: ""
  domainController:
    type: String
    description: "Specific domain controller to use for join operation (e.g., ad-ldap.sanlam.co.za) - ensures access to pre-staged computer objects"
    default: "ad-ldap.sanlam.co.za"
mainSteps:
  - action: "aws:runPowerShellScript"
    name: "JoinDomain<PERSON>ithVault<PERSON><PERSON>"
    inputs:
      runCommand:
        - |
          # Setup certificate validation bypass for vault.aws.sanlamcloud.co.za and LDAPS connections
          if ($PSVersionTable.PSVersion.Major -le 5) {
              # Windows PowerShell 5.1 and earlier - use ServicePointManager
              Write-Output "Configuring certificate validation bypass for Windows PowerShell..."
              add-type @"
          using System.Net;
          using System.Security.Cryptography.X509Certificates;
          public class TrustAllCertsPolicy : ICertificatePolicy {
              public bool CheckValidationResult(
                  ServicePoint srvPoint, X509Certificate certificate,
                  WebRequest request, int certificateProblem) {
                  return true;
              }
          }
          "@
              [System.Net.ServicePointManager]::CertificatePolicy = New-Object TrustAllCertsPolicy
          }

          # Retrieve IMDS token
          $imdsToken = Invoke-RestMethod -Method Put -Uri 'http://169.254.169.254/latest/api/token' -Headers @{'X-aws-ec2-metadata-token-ttl-seconds' = '61'}

          # Retrieve IAM role name (not ARN)
          $iamRoleRaw = Invoke-RestMethod -Uri 'http://169.254.169.254/latest/meta-data/iam/security-credentials/' -Headers @{'X-aws-ec2-metadata-token' = $imdsToken}

          # Extract just the role name if it's an ARN (format: arn:aws:iam::account:role/role-name)
          if ($iamRoleRaw -match 'arn:aws:iam::[0-9]+:role/(.+)') {
              $iamRole = $matches[1]
              Write-Output "Extracted role name from ARN: $iamRole"
          } else {
              $iamRole = $iamRoleRaw
              Write-Output "IAM Role name: $iamRole"
          }

          # Retrieve PKCS7 and remove newlines
          $pkcs7 = (Invoke-RestMethod -Uri 'http://169.254.169.254/latest/dynamic/instance-identity/pkcs7' -Headers @{'X-aws-ec2-metadata-token' = $imdsToken}).Replace("`n", "").Replace("`r", "")

          # Create JSON payload for Vault authentication
          $payload = @{
              role   = $iamRole
              pkcs7  = $pkcs7
          } | ConvertTo-Json

          Write-Output "Vault authentication payload prepared with role: $iamRole"

          # Authenticate with Vault to get client token
          Write-Output "Authenticating with Vault..."
          if ($PSVersionTable.PSVersion.Major -le 5) {
              # Windows PowerShell 5.1 - certificate bypass already configured via ServicePointManager
              $response = Invoke-RestMethod -Method Post -Uri 'https://vault.aws.sanlamcloud.co.za/v1/auth/aws-afs1/login' -ContentType 'application/json' -Body $payload
          } else {
              # PowerShell Core 6+ - use -SkipCertificateCheck parameter
              $response = Invoke-RestMethod -Method Post -Uri 'https://vault.aws.sanlamcloud.co.za/v1/auth/aws-afs1/login' -ContentType 'application/json' -Body $payload -SkipCertificateCheck
          }
          $vaultToken = $response.auth.client_token

          # Make the Vault API call to retrieve credentials
          Write-Output "Retrieving domain join credentials from Vault..."
          if ($PSVersionTable.PSVersion.Major -le 5) {
              # Windows PowerShell 5.1 - certificate bypass already configured via ServicePointManager
              $response = Invoke-RestMethod -Method Get -Uri 'https://vault.aws.sanlamcloud.co.za/v1/kv/data/MUD_AD/EC2Automation/PRD/EC2DomainJoin' -Headers @{'X-Vault-Token' = $vaultToken}
          } else {
              # PowerShell Core 6+ - use -SkipCertificateCheck parameter
              $response = Invoke-RestMethod -Method Get -Uri 'https://vault.aws.sanlamcloud.co.za/v1/kv/data/MUD_AD/EC2Automation/PRD/EC2DomainJoin' -Headers @{'X-Vault-Token' = $vaultToken} -SkipCertificateCheck
          }

          # Extract the key-value pair from data.data
          $data = $response.data.data
          if ($data) {
              # Get the first key-value pair (assuming single pair as per response)
              $userid = ($data.PSObject.Properties | Select-Object -First 1).Name
              $password = $data.$userid

              Write-Output "UserID retrieved: $userid"
          } else {
              Write-Error "No data found in response.data.data"
              exit 1
          }

          # Prepare credentials for domain join
          $securePassword = ConvertTo-SecureString $password -AsPlainText -Force
          $credential = New-Object System.Management.Automation.PSCredential($userid, $securePassword)

          # Get parameters
          $hostname = '{{ hostname }}'
          $domainName = '{{ domainName }}'
          $domainController = '{{ domainController }}'

          # Verify domain DNS resolution before attempting join
          Write-Output "Verifying DNS resolution for domain $domainName..."
          try {
              $domainResolution = Resolve-DnsName -Name $domainName -ErrorAction Stop
              Write-Output "Domain $domainName resolved successfully to: $($domainResolution.IPAddress -join ', ')"

              # Also try to resolve domain controllers
              try {
                  $dcResolution = Resolve-DnsName -Name "_ldap._tcp.dc._msdcs.$domainName" -Type SRV -ErrorAction Stop
                  Write-Output "Found $($dcResolution.Count) domain controller(s) via SRV record"
              } catch {
                  Write-Warning "Could not resolve domain controller SRV records, but will attempt join anyway: $_"
              }
          } catch {
              Write-Error "Failed to resolve domain $domainName via DNS. Check DNS settings and network connectivity: $_"
              exit 1
          }

          Write-Output "Proceeding directly to domain join (computer object existence will be validated during join process)..."

          # Join domain with new computer name, targeting specific DC to avoid replication delays
          # Note: Using -NewName parameter renames and joins in one operation
          $currentName = $env:COMPUTERNAME
          Write-Output "Joining domain $domainName as $hostname (current name: $currentName)..."
          Write-Output "Targeting domain controller $domainController to ensure access to pre-staged computer object..."
          try {
              Add-Computer -DomainName $domainName -NewName $hostname -Credential $credential -Server $domainController -Force -Verbose -ErrorAction Stop
              Write-Output "Domain join successful using DC $domainController. Computer will be renamed to $hostname upon restart."
          } catch {
              Write-Error "Failed to join domain $domainName using DC $domainController : $_"
              exit 1
          }

          # Add domain groups to local Administrators group
          $localAdminGroups = '{{ localAdminGroups }}'
          if ($localAdminGroups -and $localAdminGroups -ne '') {
              Write-Output "Adding domain groups to local Administrators group..."

              # Split the comma-separated list
              $groups = $localAdminGroups -split ','

              foreach ($group in $groups) {
                  $group = $group.Trim()
                  if ($group) {
                      try {
                          Write-Output "Adding group: $group"
                          Add-LocalGroupMember -Group "Administrators" -Member $group -ErrorAction Stop
                          Write-Output "Successfully added $group to local Administrators group"
                      } catch {
                          Write-Warning "Failed to add $group to local Administrators group: $_"
                      }
                  }
              }
          } else {
              Write-Output "No local admin groups specified, skipping group additions."
          }

          Write-Output "System will restart..."

          # Restart computer
          Restart-Computer -Force
